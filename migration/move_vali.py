import pandas as pd
import os
import re
from os import path
import subprocess
import json
import openpyxl
from openpyxl.styles import Pat<PERSON><PERSON>ill
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter

def vm_list_func(s):
    # Split the string into lines
    lines = s.strip().split('\n') #here strip removes trailing and leading zeros. Then split, splits the string on new line.

    # Extract the names from each line, excluding the header line
    names = [line.split()[0] for line in lines[1:]] #here the line is split on whitespaces and the 0th is the name.

    # Filter out empty names and strip any whitespace
    A = [name.strip() for name in names if name.strip() != '']

    return A

def extractor(i,s):
    split_parts = s.split('/')
    # Get the last part after splitting
    res = split_parts[-i] #Machine Type
    return res


#FLASK_PART_START
#Configure

#FLASK_PART_START

def mov_validate(mgg, data, path, dir, pid):

    xls = pd.ExcelFile(path)
    oname = 'validate_'+mgg+'_'+pid+'.xlsx'
    result_xl = os.path.join(dir,oname)

    json_data = json.loads(data)
    instances_list = list()

    for instance in json_data:
        instances_list.append(instance['name'])

    print(instances_list)

    #Create a Data Frame
    mg = mgg
    data = pd.DataFrame({"VM Name":[],
                        "Status":[],
                        "Move Group":[],
                        "Treatment":[],
                        "Target Size":[],
                        "Count of Disks":[],
                        "Target Disk Type":[],
                        "Project":[],
                        "Region":[],
                        "Zone":[],
                        "VPC":[],
                        "Subnet":[],
                        "Internal IP":[],
                        "External IP":[],
                        "Network Tags":[],
                        "Service Account":[],
                        "Labels":[]})


    sheets = xls.sheet_names

    df = pd.read_excel(xls, sheets[0]) #Reading the VMs sheet in VMMapping

    df.fillna("", inplace=True)


    for index, row in df.iterrows():
        try:
            print(row['Movegroup'])
        except:
            return '',"Please check whether you have uploaded the proper excel file."
        if row['Movegroup'] == mg:
            MapName = row['Instance Name']

            if MapName.lower() in instances_list:

                name = row['Name'] #VM Name

                #Network Tags in Array Form
                nettags = []
                tags = row['Network Tag'].strip().split('\n')
                for tag in tags:
                    nettags.append(tag)
                nettags.sort()
                #End of Nettags


                #option for externalIP

                if(row['Need for External IP']=='Yes' and row['External IP']==""):
                    print('in if')
                    exipopt = 'ephemeral'
                elif(row['Need for External IP']=='Yes' and row['External IP']!=""):
                    print('in elif')
                    exipopt = row['External IP']
                else:
                    exipopt = 'No External IP'


                print(exipopt)
                i = instances_list.index(name)

                json_machine_type = extractor(1, json_data[i]['machineType'])
                json_project = extractor(5, json_data[i]['selfLink'])
                json_zone = extractor(3, json_data[i]['selfLink'])
                json_region = json_zone[:-2]
                json_vpc = extractor(1, json_data[i]['networkInterfaces'][0]['network'])
                json_subnet = extractor(1, json_data[i]['networkInterfaces'][0]['subnetwork'])

                json_disktype = json_data[i]['disks'][0]['type'].lower()

                if(json_disktype == 'persistent'):
                    json_disktype = 'balanced'



                try:
                    json_nettags = json_data[i]['tags']['items']
                    json_nettags.sort()
                except KeyError:
                    json_nettags = 'No Net Tag'

                try:
                    json_labels = json_data[i]['labels']
                except KeyError:
                    json_labels = 'No Labels'

                status = 'Present('+json_data[i]['status']+')' #Status

                movegroup = row['Movegroup'] #Movegroup
                treatment = row['Migration Path'] #MigrationPath

                #TargetSize
                if(row['Target Size']==json_machine_type):
                    target_size = 'OK'
                else:
                    target_size = 'NOT OK('+json_machine_type+', should be '+row['Target Size']+')'

                #TargetDiskType
                if(row['Target Disk Type'].lower()==json_disktype): #Checks the disk type of bootdisk only
                    target_disk_type = 'OK'
                else:
                    print(row['Target Disk Type'].lower())
                    target_disk_type = 'NOT OK('+json_disktype+', should be '+row['Target Disk Type'].lower()+')'

                #DiskCount
                if(row['Disk Count']==len(json_data[0]['disks'])):
                    count_of_disks = 'OK'
                else:
                    count_of_disks = 'NOT OK('+str(len(json_data[0]['disks']))+', should be '+str(int(row['Disk Count']))+')'

                #Project
                if(extractor(1, row['Project']) == json_project):
                    project = 'OK'
                else:
                    project = 'NOT OK('+str(json_project)+', should be '+str(extractor(1, row['Project']))+')'

                #Region
                if(row['Region']==json_region):
                    region = 'OK'
                else:
                    region = 'NOT OK('+json_region+', should be '+row['Region']+')'

                #Zone
                if(row['Zone'] == json_zone):
                    zone = 'OK'
                else:
                    zone = 'NOT OK('+json_zone+', should be '+row['Zone']+')'

                #VPC
                if(row['VPC'] == json_vpc):
                    vpc = 'OK'
                else:
                    vpc = 'NOT OK('+json_vpc+', should be '+row['VPC']+')'

                #Subnet
                if(extractor(1, row['Subnet']) == json_subnet):
                    subnet = 'OK'
                else:
                    subnet = 'NOT OK('+json_subnet+', should be '+extractor(1, row['Subnet'])+')'

                #InternalIP

                if(row['Target Internal IP'] == json_data[i]['networkInterfaces'][0]['networkIP']):
                    internal_ip = 'OK'
                else:
                    internal_ip = 'NOT OK('+json_data[i]['networkInterfaces'][0]['networkIP']+', should be '+row['Target Internal IP']+')'

                #ExternalIP
                try:
                    if(row['Need for External IP']=='Yes' and type(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']) is str):
                        if(exipopt == 'ephemeral'):
                            external_ip = 'OK'
                        else:
                            if(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP'] == exipopt):
                                external_ip = 'OK'
                            else:
                                external_ip = 'NOT OK('+json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']+'should be '+exipopt+')'
                    else:
                        if(row['Need for External IP']=='No' and type(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']) is str):
                            external_ip = 'NOT OK (IP on Target Not Required)'
                        
                except KeyError:
                    if(row['Need for External IP']=='Yes'):
                        external_ip = 'NOT OK (Missing on the target)'
                    else:
                        external_ip = 'OK'

                if json_nettags == 'No Net Tag':
                    network_tags = 'NOT OK(Not Present)'
                else:
                    if(json_nettags == nettags):
                        network_tags = 'OK'
                    else:
                        network_tags = 'NOT OK( Tags should be '+str(nettags)+', but is '+str(json_nettags)+')'

                #Service Account
                if(json_data[i]['serviceAccounts'][0]['email'] == row['Service Account']):
                    service_account = 'OK'
                else:
                    service_account = 'NOT OK('+json_data[i]['serviceAccounts'][0]['email']+', should be '+row['Service Account']+')'


                label_lines = row['Labels'].split('\n')

                # Initialize an empty dictionary
                s_labels = {}

                # Iterate over the lines and split each line into key-value pairs
                for line in label_lines:
                    if line:
                        key, value = line.split(':', 1)  # Split each line into key-value pair
                        s_labels[key.strip()] = value.strip()



                if json_labels == 'No Labels':
                    labels = 'NOT OK(No Labels are Present)'
                else:
                    if(json_labels == s_labels):
                        labels = 'OK'
                    else:
                        #dict1-json_labels #dict2-s_labels
                        items_difference = {key: json_labels[key] for key in json_labels if key not in s_labels or json_labels[key] != s_labels[key]}
                        #TRue for present in VMMapping
                        #False for present in VM

                        if(items_difference.items() <= json_labels.items()):
                            labels = 'NOT OK( Additional Labels: '+str(items_difference)+' )'
                        else:
                            labels = 'NOT OK( Missing Labels: '+str(items_difference)+' )'




                print(name)
                print(status)
                print(movegroup)
                print(treatment)
                print(target_size)
                print(target_disk_type)
                print(count_of_disks)
                print(project)
                print(region)
                print(zone)
                print(vpc)
                print(subnet)
                print(internal_ip)
                print(external_ip)
                print(network_tags)
                print(service_account)
                print(labels)

                print('-------------------------------------------------------------')

            else:
                status = 'Not Present' #Status
                movegroup = row['Movegroup'] #Movegroup
                treatment = row['Migration Path'] #MigrationPath
                name = ''
                target_size = ''
                target_disk_type = ''
                count_of_disks = ''
                project = ''
                region = ''
                zone = ''
                vpc = ''
                subnet = ''
                internal_ip = ''
                external_ip = ''
                network_tags = ''
                service_account = ''
                labels = ''


                print(name)
                print(status)
                print(movegroup)
                print(treatment)
                print(target_size)
                print(target_disk_type)
                print(count_of_disks)
                print(project)
                print(region)
                print(zone)
                print(vpc)
                print(subnet)
                print(internal_ip)
                print(external_ip)
                print(network_tags)
                print(service_account)
                print(labels)


            temp = pd.DataFrame({"VM Name":[name],
                                "Status":[status],
                                "Move Group":[movegroup],
                                "Treatment":[treatment],
                                "Target Size":[target_size],
                                "Count of Disks":[count_of_disks],
                                "Target Disk Type":[target_disk_type],
                                "Project":[project],
                                "Region":[region],
                                "Zone":[zone],
                                "VPC":[vpc],
                                "Subnet":[subnet],
                                "Internal IP":[internal_ip],
                                "External IP":[external_ip],
                                "Network Tags":[network_tags],
                                "Service Account":[service_account],
                                "Labels":[labels]})

            data = pd.concat([data, temp])

    data

    data.to_excel(result_xl, index=False)

    #Formatting Part
    wb = openpyxl.load_workbook(result_xl)

    for sheet in wb.worksheets:

        val = sheet.max_column #should be replaced with max_col

        #Content for Heading Colour Filter
        for row in sheet.iter_rows(min_row=1, max_col=val, max_row=1):
            for c in row:
                c.fill = PatternFill('solid', fgColor = 'a9a9a9')

        #Dynamically edits the column width according to the content pasted there.
        dims = {}
        for row in sheet.rows:
            for cell in row:
                if cell.value:
                    dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
        for col, value in dims.items():
            sheet.column_dimensions[col].width = value+2

    # Set the column width as +2 greater than the first row's width
        for col, width in dims.items():
            sheet.column_dimensions[col].width = width + 6

        for row in sheet.rows:
                for cell in row:
                    if cell.value == 'OK':
                        cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                    elif 'NOT OK' in str(cell.value):
                        cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Red

                    if 'Present(' in str(cell.value):
                        cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                    elif 'Not Present' in str(cell.value):
                        cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Green

        #Autofilter for all headings
        sheet.auto_filter.ref = sheet.dimensions


        #Remove ; and replace with new line
        for row in sheet.iter_rows():
            for cell in row:
                # Check if the cell value is a string
                if isinstance(cell.value, str):
                    # Use re.sub to replace the text in the cell
                    cell.value = re.sub(r' ; ', chr(13)+chr(10), cell.value)

    wb.save(result_xl)

    return result_xl, ''


def aws_mov_validate(mgg, inventory_df, mapping_df, dir, pid):

    oname = 'validate_'+mgg+'_'+pid+'.xlsx'
    result_xl = os.path.join(dir,oname)

    instances_list = inventory_df['Instance ID'].tolist()

    mapping_df.fillna("", inplace=True)

    #Create a Data Frame
    mg = mgg
    data = pd.DataFrame({"VM Name":[],
                        "Status":[],
                        "Move Group":[],
                        "Treatment":[],
                        "Target Size":[],
                        "Count of Disks":[],
                        "Target Disk Type":[],
                        "Region":[],
                        "VPC":[],
                        "Subnet":[],
                        "Internal IP":[],
                        "External IP":[],
                        "Security Groups":[],
                        "IAM Role":[],
                        "Tags":[]})

    for index, row in mapping_df.iterrows():
        try:
            print(row['Movegroup'])
        except:
            return '',"Please check whether you have uploaded the proper excel file."
        if row['Movegroup'] == mg:
            MapName = row['Instance ID(AWS)']

            if MapName in instances_list:

                name = row['Instance ID(AWS)'] #VM Name

                #Security Groups in Array Form
                sgs = []
                groups = row['Security Group Id'].strip().split('\n')
                for group in groups:
                    sgs.append(group)
                sgs.sort()
                #End of Security Groups


                #option for externalIP

                if(row['Need for External IP']=='Yes' and row['External IP']==""):
                    print('in if')
                    exipopt = 'ephemeral'
                elif(row['Need for External IP']=='Yes' and row['External IP']!=""):
                    print('in elif')
                    exipopt = row['External IP']
                else:
                    exipopt = 'No External IP'


                print(exipopt)
                
                # Find the instance in the inventory
                instance_data = inventory_df.loc[inventory_df['Instance ID'] == MapName].iloc[0]
                
                json_machine_type = instance_data['Instance Type']
                json_region = instance_data['Region']
                json_vpc = instance_data['VPC']
                json_subnet = instance_data['Subnet']
                json_disktype = instance_data['Root Device Type']

                try:
                    json_sgs = instance_data['Security Groups']
                    if isinstance(json_sgs, str):
                        json_sgs = json_sgs.split(',')
                    json_sgs.sort()
                except KeyError:
                    json_sgs = 'No Security Groups'

                try:
                    json_iam = instance_data['IAM Instance Profile']
                except KeyError:
                    json_iam = 'No IAM Role'
                
                try:
                    json_tags = instance_data['Tags']
                    if isinstance(json_tags, str):
                        # Assume tags are in format key:value,key:value
                        json_tags = dict(item.split(':') for item in json_tags.split(','))
                except KeyError:
                    json_tags = 'No Tags'


                status = 'Present('+instance_data['State']+')' #Status

                movegroup = row['Movegroup'] #Movegroup
                treatment = row['Migration Path'] #MigrationPath

                #TargetSize
                if(row['Target Size']==json_machine_type):
                    target_size = 'OK'
                else:
                    target_size = 'NOT OK('+json_machine_type+', should be '+row['Target Size']+')'

                #TargetDiskType
                if(row['Target Disk Type'].lower()==json_disktype.lower()): #Checks the disk type of bootdisk only
                    target_disk_type = 'OK'
                else:
                    target_disk_type = 'NOT OK('+json_disktype+', should be '+row['Target Disk Type'].lower()+')'

                #DiskCount
                try:
                    disk_count = instance_data['Disk Count']
                except KeyError:
                    disk_count = 1  # assume 1 if not present
                if(row['Disk Count']==disk_count):
                    count_of_disks = 'OK'
                else:
                    count_of_disks = 'NOT OK('+str(disk_count)+', should be '+str(int(row['Disk Count']))+')'

                #Region
                if(row['Region']==json_region):
                    region = 'OK'
                else:
                    region = 'NOT OK('+json_region+', should be '+row['Region']+')'

                #VPC
                if(row['VPC'] == json_vpc):
                    vpc = 'OK'
                else:
                    vpc = 'NOT OK('+json_vpc+', should be '+row['VPC']+')'

                #Subnet
                if(row['Subnet Id'] == json_subnet):
                    subnet = 'OK'
                else:
                    subnet = 'NOT OK('+json_subnet+', should be '+row['Subnet Id']+')'

                #InternalIP
                try:
                    private_ip = instance_data['Private IP']
                except KeyError:
                    private_ip = instance_data['PrivateIpAddress']
                if(row['Target Internal IP'] == private_ip):
                    internal_ip = 'OK'
                else:
                    internal_ip = 'NOT OK('+private_ip+', should be '+row['Target Internal IP']+')'

                #ExternalIP
                try:
                    public_ip = instance_data['Public IP']
                    if(row['Need for External IP']=='Yes' and public_ip):
                        if(exipopt == 'ephemeral'):
                            external_ip = 'OK'
                        else:
                            if(public_ip == exipopt):
                                external_ip = 'OK'
                            else:
                                external_ip = 'NOT OK('+public_ip+' should be '+exipopt+')'
                    else:
                        if(row['Need for External IP']=='No' and public_ip):
                            external_ip = 'NOT OK (IP on Target Not Required)'
                        else:
                            external_ip = 'OK'
                        
                except KeyError:
                    if(row['Need for External IP']=='Yes'):
                        external_ip = 'NOT OK (Missing on the target)'
                    else:
                        external_ip = 'OK'

                if json_sgs == 'No Security Groups':
                    security_groups = 'NOT OK(Not Present)'
                else:
                    if(json_sgs == sgs):
                        security_groups = 'OK'
                    else:
                        security_groups = 'NOT OK( Security Groups should be '+str(sgs)+', but is '+str(json_sgs)+')'

                #IAM Role
                if(json_iam == row['IAM-Profile']):
                    iam_role = 'OK'
                else:
                    iam_role = 'NOT OK('+json_iam+', should be '+row['IAM-Profile']+')'


                tag_lines = row['Tags'].split('\n')

                # Initialize an empty dictionary
                s_tags = {}

                # Iterate over the lines and split each line into key-value pairs
                for line in tag_lines:
                    if line:
                        key, value = line.split(':', 1)  # Split each line into key-value pair
                        s_tags[key.strip()] = value.strip()



                if json_tags == 'No Tags':
                    tags = 'NOT OK(No Tags are Present)'
                else:
                    if(json_tags == s_tags):
                        tags = 'OK'
                    else:
                        #dict1-json_tags #dict2-s_tags
                        items_difference = {key: json_tags[key] for key in json_tags if key not in s_tags or json_tags[key] != s_tags[key]}
                        #TRue for present in VMMapping
                        #False for present in VM

                        if(items_difference.items() <= json_tags.items()):
                            tags = 'NOT OK( Additional Tags: '+str(items_difference)+' )'
                        else:
                            tags = 'NOT OK( Missing Tags: '+str(items_difference)+' )'

            else:
                status = 'Not Present' #Status
                movegroup = row['Movegroup'] #Movegroup
                treatment = row['Migration Path'] #MigrationPath
                name = ''
                target_size = ''
                target_disk_type = ''
                count_of_disks = ''
                region = ''
                vpc = ''
                subnet = ''
                internal_ip = ''
                external_ip = ''
                security_groups = ''
                iam_role = ''
                tags = ''


            temp = pd.DataFrame({"VM Name":[name],
                                "Status":[status],
                                "Move Group":[movegroup],
                                "Treatment":[treatment],
                                "Target Size":[target_size],
                                "Count of Disks":[count_of_disks],
                                "Target Disk Type":[target_disk_type],
                                "Region":[region],
                                "VPC":[vpc],
                                "Subnet":[subnet],
                                "Internal IP":[internal_ip],
                                "External IP":[external_ip],
                                "Security Groups":[security_groups],
                                "IAM Role":[iam_role],
                                "Tags":[tags]})

            data = pd.concat([data, temp])

    data.to_excel(result_xl, index=False)

    #Formatting Part
    wb = openpyxl.load_workbook(result_xl)

    for sheet in wb.worksheets:

        val = sheet.max_column #should be replaced with max_col

        #Content for Heading Colour Filter
        for row in sheet.iter_rows(min_row=1, max_col=val, max_row=1):
            for c in row:
                c.fill = PatternFill('solid', fgColor = 'a9a9a9')

        #Dynamically edits the column width according to the content pasted there.
        dims = {}
        for row in sheet.rows:
            for cell in row:
                if cell.value:
                    dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
        for col, value in dims.items():
            sheet.column_dimensions[col].width = value+2

    # Set the column width as +2 greater than the first row's width
        for col, width in dims.items():
            sheet.column_dimensions[col].width = width + 6

        for row in sheet.rows:
                for cell in row:
                    if cell.value == 'OK':
                        cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                    elif 'NOT OK' in str(cell.value):
                        cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Red

                    if 'Present(' in str(cell.value):
                        cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                    elif 'Not Present' in str(cell.value):
                        cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Green

        #Autofilter for all headings
        sheet.auto_filter.ref = sheet.dimensions


        #Remove ; and replace with new line
        for row in sheet.iter_rows():
            for cell in row:
                # Check if the cell value is a string
                if isinstance(cell.value, str):
                    # Use re.sub to replace the text in the cell
                    cell.value = re.sub(r' ; ', chr(13)+chr(10), cell.value)

    wb.save(result_xl)

    return result_xl, ''

def aws_move_vali(request,rootpath):

    dir1 = os.path.join(rootpath, 'migration', 'aws_mov_val')

    # Create the directory if it doesn't exist
    if not os.path.exists(dir1):
        os.makedirs(dir1)

    from inventory.aws_inventory.aws import ec2
    import pandas as pd

    #Get data from form and save it
    mg = request.form['move-group']
    arn = request.form['arn']
    file = request.files['excel-file']
    file.save(os.path.join(dir1, file.filename))
    excel_path = os.path.join(dir1, file.filename)

    # Get credentials from ARN
    import boto3
    from config.awsconfig import config as aws_config
    if arn:
        # Use awsconfig.py to get credentials and write to ~/.aws/credentials
        err = aws_config(arn, "", False, False, "inventory")
        if err:
            raise Exception(err)
        # Now create boto3 session without explicit credentials, boto3 will use ~/.aws/credentials
        session = boto3.Session()
        credentials = {
            'aws_access_key_id': session.get_credentials().access_key,
            'aws_secret_access_key': session.get_credentials().secret_key,
            'aws_session_token': session.get_credentials().token
        }
    else:
        credentials = {}

    # Generate inventory
    inventory_data = ec2(regions=[], profile_name=credentials)
    inventory_df = pd.DataFrame(inventory_data['EC2'][1:], columns=inventory_data['EC2'][0])
    
    # Read the mapping sheet
    mapping_df = pd.read_excel(excel_path, sheet_name='VMs')

    # Validate
    file_path, error = aws_mov_validate(mg, inventory_df, mapping_df, dir1, arn)

    if os.path.isfile(file_path):
        msg = "Validation file created and downloaded successfully."
        return file_path,"",msg,0
    else:
        err1 = "Internal Error"
        return "",err1,error,1

def move_vali(request,rootpath):

    dir1 = os.path.join(rootpath, 'migration', 'move_val')

    #Get data from form and save it
    mg = request.form['move-group']
    pj = request.form['projid']
    file = request.files['excel-file']
    file.save(os.path.join(dir1, file.filename))
    # print(pj)
    excel_path = os.path.join(dir1, file.filename)

    config = subprocess.run(['/usr/bin/pwsh','-c','gcloud', 'config', 'set', 'project', pj], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

    config_out = config.stderr

    print(config_out)

    if 'WARNING:' in config_out:
        lines = config_out.strip().split('.')
        msg = 'Check whether access is given to the mentioned service account.'
        err = lines[0]+'\n or Check whether access is given to the mentioned service account'
        return '',err,msg,1

    gcloud_int = 'gcloud compute instances list --format=json'
    instances = subprocess.run(['/usr/bin/pwsh','-c', gcloud_int], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

    data = instances.stdout

    #Calling our script passing mg and the filename(file will be in same dir as python script so no full path)
    file_path, error = mov_validate(mg, data, excel_path, dir1, pj)

    if os.path.isfile(file_path):
        msg = "Validation file created and downloaded successfully."
        return file_path,"",msg,0
    else:
        err1 = "Internal Error"
        return "",err1,error,1